#!/bin/bash

echo "🌟 LED流水灯测试脚本 🌟"
echo "======================="

# 创建LED设备目录（如果不存在）
echo "设置LED设备文件..."
sudo mkdir -p /sys/class/leds/led1
sudo mkdir -p /sys/class/leds/led2
sudo mkdir -p /sys/class/leds/led3

# 创建brightness文件
sudo touch /sys/class/leds/led1/brightness
sudo touch /sys/class/leds/led2/brightness
sudo touch /sys/class/leds/led3/brightness

# 设置权限
sudo chmod 666 /sys/class/leds/led1/brightness
sudo chmod 666 /sys/class/leds/led2/brightness
sudo chmod 666 /sys/class/leds/led3/brightness

echo "✅ LED设备文件设置完成"
echo ""

# 检查可执行文件
if [ ! -f "./led_simple" ]; then
    echo "⚠️  led_simple 可执行文件不存在，正在编译..."
    cd build
    make
    cd ..
    
    if [ ! -f "./build/led_simple" ]; then
        echo "❌ 编译失败，请检查代码"
        exit 1
    else
        echo "✅ 编译成功"
        cp ./build/led_simple ./led_simple
    fi
fi

echo "🚀 启动LED流水灯程序..."
echo "模式：1→2→3→3→2→1 循环，然后全闪烁3次"
echo "按 Ctrl+C 停止程序"
echo ""

# 运行LED流水灯程序
./led_simple
