#ifndef _FILE_H_
#define _FILE_H_

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>

// 基础文件操作函数
int gpio_get(const char *chip_name, int line_num);
int write_data(const char *path,const char *data);
int read_data(const char *path,float *data);
int buzzer_set(unsigned int value);

// 设备控制函数
int control_fan(int speed);
void led_blink(int times);

// 传感器数据读取和计算函数
float read_temperature(void);
float read_humidity(void);

#endif