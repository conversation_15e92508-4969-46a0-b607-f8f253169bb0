#include "file.h"

// 系统路径定义（在头文件中声明，这里定义）
const char *led1_path = "/sys/class/leds/led1/brightness";
const char *led2_path = "/sys/class/leds/led2/brightness";
const char *led3_path = "/sys/class/leds/led3/brightness";

int write_data(const char *path,const char *data)
{
    FILE *fd = fopen(path,"w");
    if(fd == NULL)
    {
        printf("%s error\n",path);
        return -1;
    }
    fprintf(fd,"%s",data);
    fclose(fd);
    return 0;
}

int read_data(const char *path,float *data)
{
    FILE *fd = fopen(path,"r");
    if(fd == NULL)
    {
        printf("%s error\n",path);
        return -1;
    }
    fscanf(fd,"%f",data);
    fclose(fd);
    return 0;
}

// 控制风扇函数
int control_fan(int speed)
{
    const char *fan_path = "/sys/class/hwmon/hwmon0/pwm1";
    char speed_str[10];
    sprintf(speed_str, "%d", speed);
    return write_data(fan_path, speed_str);
}

// LED闪烁函数
void led_blink(int times)
{
    for(int i = 0; i < times; i++)
    {
        // 打开所有LED
        write_data(led1_path, "1");
        write_data(led2_path, "1");
        write_data(led3_path, "1");
        usleep(500000); // 0.5秒

        // 关闭所有LED
        write_data(led1_path, "0");
        write_data(led2_path, "0");
        write_data(led3_path, "0");
        usleep(500000); // 0.5秒
    }
}

// 读取温度传感器数据并计算实际温度值
float read_temperature(void)
{
    const char *temp_raw_path = "/sys/bus/iio/devices/iio:device0/in_temp_raw";
    const char *temp_offset_path = "/sys/bus/iio/devices/iio:device0/in_temp_offset";
    const char *temp_scale_path = "/sys/bus/iio/devices/iio:device0/in_temp_scale";

    float raw = 0.0f, offset = 0.0f, scale = 0.0f;
    float temp = 25.0f; // 默认温度值

    // 读取原始值、偏移量和缩放因子
    if(read_data(temp_raw_path, &raw) == 0 &&
       read_data(temp_offset_path, &offset) == 0 &&
       read_data(temp_scale_path, &scale) == 0)
    {
        // 温度计算（公式需参考硬件手册）
        temp = (raw + offset) * scale / 1000.0f;
    }
    else
    {
        printf("温度传感器数据读取失败，使用默认值\n");
    }

    return temp;
}

// 读取湿度传感器数据并计算实际湿度值
float read_humidity(void)
{
    const char *humidity_raw_path = "/sys/bus/iio/devices/iio:device0/in_humidityrelative_raw";
    const char *humidity_offset_path = "/sys/bus/iio/devices/iio:device0/in_humidityrelative_offset";
    const char *humidity_scale_path = "/sys/bus/iio/devices/iio:device0/in_humidityrelative_scale";

    float raw = 0.0f, offset = 0.0f, scale = 0.0f;
    float humi = 50.0f; // 默认湿度值

    // 读取原始值、偏移量和缩放因子
    if(read_data(humidity_raw_path, &raw) == 0)
    {
        // 尝试读取偏移量和缩放因子（可能不存在）
        if(read_data(humidity_offset_path, &offset) != 0)
        {
            offset = 0.0f; // 如果没有偏移量文件，使用0
        }
        if(read_data(humidity_scale_path, &scale) != 0)
        {
            scale = 1.0f; // 如果没有缩放因子文件，使用1
        }

        // 湿度计算（公式需参考硬件手册）
        humi = (raw + offset) * scale / 1000.0f;

        // 确保湿度值在合理范围内 (0-100%)
        if(humi < 0) humi = 0;
        if(humi > 100) humi = 100;
    }
    else
    {
        printf("湿度传感器数据读取失败，使用默认值\n");
    }

    return humi;
}
