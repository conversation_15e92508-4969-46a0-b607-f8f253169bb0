#include "file.h"
#include <unistd.h>
#include <stdio.h>

// LED流水灯控制函数
void led_flow_pattern(void)
{
    printf("=== LED流水灯启动 ===\n");
    printf("模式: 1->2->3->3->2->1 循环，然后全闪烁3次\n\n");
    
    while(1)
    {
        printf("开始流水灯循环...\n");
        
        // 123321 流水灯模式
        // LED1 亮
        printf("LED1 亮\n");
        write_data("/sys/class/leds/led1/brightness", "1");
        write_data("/sys/class/leds/led2/brightness", "0");
        write_data("/sys/class/leds/led3/brightness", "0");
        usleep(500000); // 0.5秒
        
        // LED2 亮
        printf("LED2 亮\n");
        write_data("/sys/class/leds/led1/brightness", "0");
        write_data("/sys/class/leds/led2/brightness", "1");
        write_data("/sys/class/leds/led3/brightness", "0");
        usleep(500000); // 0.5秒
        
        // LED3 亮
        printf("LED3 亮\n");
        write_data("/sys/class/leds/led1/brightness", "0");
        write_data("/sys/class/leds/led2/brightness", "0");
        write_data("/sys/class/leds/led3/brightness", "1");
        usleep(500000); // 0.5秒
        
        // LED3 再次亮（3->3）
        printf("LED3 亮\n");
        write_data("/sys/class/leds/led1/brightness", "0");
        write_data("/sys/class/leds/led2/brightness", "0");
        write_data("/sys/class/leds/led3/brightness", "1");
        usleep(500000); // 0.5秒
        
        // LED2 亮（回程）
        printf("LED2 亮\n");
        write_data("/sys/class/leds/led1/brightness", "0");
        write_data("/sys/class/leds/led2/brightness", "1");
        write_data("/sys/class/leds/led3/brightness", "0");
        usleep(500000); // 0.5秒
        
        // LED1 亮（回程）
        printf("LED1 亮\n");
        write_data("/sys/class/leds/led1/brightness", "1");
        write_data("/sys/class/leds/led2/brightness", "0");
        write_data("/sys/class/leds/led3/brightness", "0");
        usleep(500000); // 0.5秒
        
        // 关闭所有LED，准备全闪烁
        write_data("/sys/class/leds/led1/brightness", "0");
        write_data("/sys/class/leds/led2/brightness", "0");
        write_data("/sys/class/leds/led3/brightness", "0");
        usleep(300000); // 0.3秒间隔
        
        printf("\n--- 全闪烁3次 ---\n");
        
        // 全闪烁3次
        for(int i = 0; i < 3; i++)
        {
            printf("全闪烁 第%d次 - 亮\n", i + 1);
            // 所有LED同时亮
            write_data("/sys/class/leds/led1/brightness", "1");
            write_data("/sys/class/leds/led2/brightness", "1");
            write_data("/sys/class/leds/led3/brightness", "1");
            usleep(400000); // 0.4秒
            
            printf("全闪烁 第%d次 - 灭\n", i + 1);
            // 所有LED同时灭
            write_data("/sys/class/leds/led1/brightness", "0");
            write_data("/sys/class/leds/led2/brightness", "0");
            write_data("/sys/class/leds/led3/brightness", "0");
            usleep(400000); // 0.4秒
        }
        
        printf("\n=== 一轮循环完成，2秒后开始下一轮 ===\n\n");
        sleep(2); // 等待2秒开始下一轮
    }
}

// 单独的LED控制函数（用于测试）
void led_single_test(void)
{
    printf("=== LED单独测试 ===\n");
    
    printf("测试LED1...\n");
    write_data("/sys/class/leds/led1/brightness", "1");
    sleep(1);
    write_data("/sys/class/leds/led1/brightness", "0");
    
    printf("测试LED2...\n");
    write_data("/sys/class/leds/led2/brightness", "1");
    sleep(1);
    write_data("/sys/class/leds/led2/brightness", "0");
    
    printf("测试LED3...\n");
    write_data("/sys/class/leds/led3/brightness", "1");
    sleep(1);
    write_data("/sys/class/leds/led3/brightness", "0");
    
    printf("LED测试完成\n\n");
}

// 关闭所有LED
void led_all_off(void)
{
    write_data("/sys/class/leds/led1/brightness", "0");
    write_data("/sys/class/leds/led2/brightness", "0");
    write_data("/sys/class/leds/led3/brightness", "0");
    printf("所有LED已关闭\n");
}

int main()
{
    printf("🌟 LED流水灯控制程序 🌟\n");
    printf("=======================\n");
    printf("功能说明：\n");
    printf("1. LED流水灯：1->2->3->3->2->1 循环\n");
    printf("2. 每轮循环后全闪烁3次\n");
    printf("3. 按 Ctrl+C 停止程序\n\n");
    
    // 初始化：关闭所有LED
    led_all_off();
    sleep(1);
    
    // 可选：先进行LED测试
    char test_choice;
    printf("是否先进行LED测试？(y/n): ");
    scanf("%c", &test_choice);
    
    if(test_choice == 'y' || test_choice == 'Y')
    {
        led_single_test();
    }
    
    printf("开始LED流水灯程序...\n\n");
    
    // 开始流水灯循环
    led_flow_pattern();
    
    return 0;
}
