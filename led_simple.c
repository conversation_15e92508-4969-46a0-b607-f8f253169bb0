#include "file.h"
#include <unistd.h>
#include <stdio.h>
#include <signal.h>
#include <stdlib.h>

// 全局变量控制程序运行
volatile int running = 1;

// 信号处理函数
void signal_handler(int sig)
{
    if(sig == SIGINT)
    {
        printf("\n\n收到停止信号，正在关闭所有LED...\n");
        write_data("/sys/class/leds/led1/brightness", "0");
        write_data("/sys/class/leds/led2/brightness", "0");
        write_data("/sys/class/leds/led3/brightness", "0");
        printf("程序已安全退出\n");
        running = 0;
        exit(0);
    }
}

// LED流水灯主函数
void led_flow_123321(void)
{
    int cycle_count = 0;
    
    while(running)
    {
        cycle_count++;
        printf("=== 第%d轮循环 ===\n", cycle_count);
        
        // 1->2->3->3->2->1 流水灯序列
        int sequence[] = {1, 2, 3, 3, 2, 1};
        int sequence_length = 6;
        
        for(int i = 0; i < sequence_length && running; i++)
        {
            // 关闭所有LED
            write_data("/sys/class/leds/led1/brightness", "0");
            write_data("/sys/class/leds/led2/brightness", "0");
            write_data("/sys/class/leds/led3/brightness", "0");
            
            // 点亮对应的LED
            switch(sequence[i])
            {
                case 1:
                    printf("💡 LED1 亮\n");
                    write_data("/sys/class/leds/led1/brightness", "1");
                    break;
                case 2:
                    printf("💡 LED2 亮\n");
                    write_data("/sys/class/leds/led2/brightness", "1");
                    break;
                case 3:
                    printf("💡 LED3 亮\n");
                    write_data("/sys/class/leds/led3/brightness", "1");
                    break;
            }
            
            usleep(500000); // 0.5秒延时
        }
        
        // 关闭所有LED，准备全闪烁
        write_data("/sys/class/leds/led1/brightness", "0");
        write_data("/sys/class/leds/led2/brightness", "0");
        write_data("/sys/class/leds/led3/brightness", "0");
        usleep(300000); // 0.3秒间隔
        
        printf("✨ 全闪烁3次\n");
        
        // 全闪烁3次
        for(int flash = 0; flash < 3 && running; flash++)
        {
            printf("   闪烁 %d/3 - 亮\n", flash + 1);
            // 所有LED同时亮
            write_data("/sys/class/leds/led1/brightness", "1");
            write_data("/sys/class/leds/led2/brightness", "1");
            write_data("/sys/class/leds/led3/brightness", "1");
            usleep(400000); // 0.4秒
            
            printf("   闪烁 %d/3 - 灭\n", flash + 1);
            // 所有LED同时灭
            write_data("/sys/class/leds/led1/brightness", "0");
            write_data("/sys/class/leds/led2/brightness", "0");
            write_data("/sys/class/leds/led3/brightness", "0");
            usleep(400000); // 0.4秒
        }
        
        printf("--- 循环完成，2秒后开始下一轮 ---\n\n");
        sleep(2); // 等待2秒
    }
}

// 启动动画
void startup_animation(void)
{
    printf("🚀 LED流水灯启动中...\n");
    
    // 快速测试所有LED
    for(int i = 0; i < 2; i++)
    {
        write_data("/sys/class/leds/led1/brightness", "1");
        usleep(100000);
        write_data("/sys/class/leds/led1/brightness", "0");
        
        write_data("/sys/class/leds/led2/brightness", "1");
        usleep(100000);
        write_data("/sys/class/leds/led2/brightness", "0");
        
        write_data("/sys/class/leds/led3/brightness", "1");
        usleep(100000);
        write_data("/sys/class/leds/led3/brightness", "0");
    }
    
    printf("✅ 启动完成！\n\n");
}

int main()
{
    // 设置信号处理
    signal(SIGINT, signal_handler);
    
    printf("🌟 LED流水灯程序 - 123321模式 🌟\n");
    printf("================================\n");
    printf("模式说明：\n");
    printf("• 流水灯序列：1→2→3→3→2→1\n");
    printf("• 每轮结束后全闪烁3次\n");
    printf("• 按 Ctrl+C 安全退出\n");
    printf("================================\n\n");
    
    // 初始化：关闭所有LED
    write_data("/sys/class/leds/led1/brightness", "0");
    write_data("/sys/class/leds/led2/brightness", "0");
    write_data("/sys/class/leds/led3/brightness", "0");
    
    // 启动动画
    startup_animation();
    
    // 开始主循环
    printf("开始LED流水灯循环...\n\n");
    led_flow_123321();
    
    return 0;
}
