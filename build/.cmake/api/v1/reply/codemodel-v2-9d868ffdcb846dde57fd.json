{"configurations": [{"directories": [{"build": ".", "minimumCMakeVersion": {"string": "3.10.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "led", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "demo::@6890427a1f51a3e7e1df", "jsonFile": "target-demo-Debug-69810f9acfa44bf87191.json", "name": "demo", "projectIndex": 0}, {"directoryIndex": 0, "id": "fan::@6890427a1f51a3e7e1df", "jsonFile": "target-fan-Debug-cd28340751658d159bcf.json", "name": "fan", "projectIndex": 0}, {"directoryIndex": 0, "id": "led1::@6890427a1f51a3e7e1df", "jsonFile": "target-led1-Debug-0535f1f506ca7658f9af.json", "name": "led1", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/led/build", "source": "/home/<USER>/led"}, "version": {"major": 2, "minor": 1}}