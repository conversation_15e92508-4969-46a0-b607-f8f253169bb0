{"artifacts": [{"path": "fan"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 6, "parent": 0}]}, "compileGroups": [{"language": "C", "sourceIndexes": [0]}], "id": "fan::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "", "role": "flags"}], "language": "C"}, "name": "fan", "nameOnDisk": "fan", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "fan.c", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}