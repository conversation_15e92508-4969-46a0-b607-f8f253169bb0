{"artifacts": [{"path": "led1"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 4, "parent": 0}]}, "compileGroups": [{"language": "C", "sourceIndexes": [0, 1]}], "id": "led1::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "", "role": "flags"}], "language": "C"}, "name": "led1", "nameOnDisk": "led1", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "led.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "file.c", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}