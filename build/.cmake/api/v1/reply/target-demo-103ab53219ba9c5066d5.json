{"artifacts": [{"path": "demo"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 5, "parent": 0}]}, "compileGroups": [{"language": "C", "sourceIndexes": [0, 1]}], "id": "demo::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "", "role": "flags"}], "language": "C"}, "name": "demo", "nameOnDisk": "demo", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "demo.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "file.c", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}