{"artifacts": [{"path": "led_flow"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 7, "parent": 0}]}, "compileGroups": [{"language": "C", "sourceIndexes": [0, 1]}], "id": "led_flow::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "", "role": "flags"}], "language": "C"}, "name": "led_flow", "nameOnDisk": "led_flow", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "led_flow.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "file.c", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}