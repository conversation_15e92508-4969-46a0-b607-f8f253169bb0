{"configurations": [{"directories": [{"build": ".", "minimumCMakeVersion": {"string": "3.10.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "led", "targetIndexes": [0, 1, 2, 3, 4]}], "targets": [{"directoryIndex": 0, "id": "demo::@6890427a1f51a3e7e1df", "jsonFile": "target-demo-103ab53219ba9c5066d5.json", "name": "demo", "projectIndex": 0}, {"directoryIndex": 0, "id": "fan::@6890427a1f51a3e7e1df", "jsonFile": "target-fan-6a8fee00b12ae9512ea3.json", "name": "fan", "projectIndex": 0}, {"directoryIndex": 0, "id": "led1::@6890427a1f51a3e7e1df", "jsonFile": "target-led1-3ebb35d7f66b4c0120e1.json", "name": "led1", "projectIndex": 0}, {"directoryIndex": 0, "id": "led_flow::@6890427a1f51a3e7e1df", "jsonFile": "target-led_flow-4ddd03a36556d4b5be5f.json", "name": "led_flow", "projectIndex": 0}, {"directoryIndex": 0, "id": "led_simple::@6890427a1f51a3e7e1df", "jsonFile": "target-led_simple-b322f547e804c816516d.json", "name": "led_simple", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/led/build", "source": "/home/<USER>/led"}, "version": {"major": 2, "minor": 1}}