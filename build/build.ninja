# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.18

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: led
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/led/build && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/led -B/home/<USER>/led/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/led/build && /usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util

# =============================================================================
# Object build statements for EXECUTABLE target fan


#############################################
# Order-only phony target for fan

build cmake_object_order_depends_target_fan: phony || CMakeFiles/fan.dir

build CMakeFiles/fan.dir/fan.c.o: C_COMPILER__fan_Debug ../fan.c || cmake_object_order_depends_target_fan
  DEP_FILE = CMakeFiles/fan.dir/fan.c.o.d
  FLAGS = -g
  OBJECT_DIR = CMakeFiles/fan.dir
  OBJECT_FILE_DIR = CMakeFiles/fan.dir
  TARGET_COMPILE_PDB = CMakeFiles/fan.dir/
  TARGET_PDB = fan.pdb


# =============================================================================
# Link build statements for EXECUTABLE target fan


#############################################
# Link the executable fan

build fan: C_EXECUTABLE_LINKER__fan_Debug CMakeFiles/fan.dir/fan.c.o
  FLAGS = -g
  OBJECT_DIR = CMakeFiles/fan.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = CMakeFiles/fan.dir/
  TARGET_FILE = fan
  TARGET_PDB = fan.pdb

# =============================================================================
# Object build statements for EXECUTABLE target demo


#############################################
# Order-only phony target for demo

build cmake_object_order_depends_target_demo: phony || CMakeFiles/demo.dir

build CMakeFiles/demo.dir/demo.c.o: C_COMPILER__demo_Debug ../demo.c || cmake_object_order_depends_target_demo
  DEP_FILE = CMakeFiles/demo.dir/demo.c.o.d
  FLAGS = -g
  OBJECT_DIR = CMakeFiles/demo.dir
  OBJECT_FILE_DIR = CMakeFiles/demo.dir
  TARGET_COMPILE_PDB = CMakeFiles/demo.dir/
  TARGET_PDB = demo.pdb

build CMakeFiles/demo.dir/file.c.o: C_COMPILER__demo_Debug ../file.c || cmake_object_order_depends_target_demo
  DEP_FILE = CMakeFiles/demo.dir/file.c.o.d
  FLAGS = -g
  OBJECT_DIR = CMakeFiles/demo.dir
  OBJECT_FILE_DIR = CMakeFiles/demo.dir
  TARGET_COMPILE_PDB = CMakeFiles/demo.dir/
  TARGET_PDB = demo.pdb


# =============================================================================
# Link build statements for EXECUTABLE target demo


#############################################
# Link the executable demo

build demo: C_EXECUTABLE_LINKER__demo_Debug CMakeFiles/demo.dir/demo.c.o CMakeFiles/demo.dir/file.c.o
  FLAGS = -g
  OBJECT_DIR = CMakeFiles/demo.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = CMakeFiles/demo.dir/
  TARGET_FILE = demo
  TARGET_PDB = demo.pdb

# =============================================================================
# Object build statements for EXECUTABLE target led1


#############################################
# Order-only phony target for led1

build cmake_object_order_depends_target_led1: phony || CMakeFiles/led1.dir

build CMakeFiles/led1.dir/led.c.o: C_COMPILER__led1_Debug ../led.c || cmake_object_order_depends_target_led1
  DEP_FILE = CMakeFiles/led1.dir/led.c.o.d
  FLAGS = -g
  OBJECT_DIR = CMakeFiles/led1.dir
  OBJECT_FILE_DIR = CMakeFiles/led1.dir
  TARGET_COMPILE_PDB = CMakeFiles/led1.dir/
  TARGET_PDB = led1.pdb

build CMakeFiles/led1.dir/file.c.o: C_COMPILER__led1_Debug ../file.c || cmake_object_order_depends_target_led1
  DEP_FILE = CMakeFiles/led1.dir/file.c.o.d
  FLAGS = -g
  OBJECT_DIR = CMakeFiles/led1.dir
  OBJECT_FILE_DIR = CMakeFiles/led1.dir
  TARGET_COMPILE_PDB = CMakeFiles/led1.dir/
  TARGET_PDB = led1.pdb


# =============================================================================
# Link build statements for EXECUTABLE target led1


#############################################
# Link the executable led1

build led1: C_EXECUTABLE_LINKER__led1_Debug CMakeFiles/led1.dir/led.c.o CMakeFiles/led1.dir/file.c.o
  FLAGS = -g
  OBJECT_DIR = CMakeFiles/led1.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = CMakeFiles/led1.dir/
  TARGET_FILE = led1
  TARGET_PDB = led1.pdb

# =============================================================================
# Target aliases.

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /home/<USER>/led/build

build all: phony fan demo led1

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | ../CMakeLists.txt /usr/share/cmake-3.18/Modules/CMakeCCompiler.cmake.in /usr/share/cmake-3.18/Modules/CMakeCCompilerABI.c /usr/share/cmake-3.18/Modules/CMakeCInformation.cmake /usr/share/cmake-3.18/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /usr/share/cmake-3.18/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake-3.18/Modules/CMakeCompilerIdDetection.cmake /usr/share/cmake-3.18/Modules/CMakeDetermineCCompiler.cmake /usr/share/cmake-3.18/Modules/CMakeDetermineCompileFeatures.cmake /usr/share/cmake-3.18/Modules/CMakeDetermineCompiler.cmake /usr/share/cmake-3.18/Modules/CMakeDetermineCompilerABI.cmake /usr/share/cmake-3.18/Modules/CMakeDetermineCompilerId.cmake /usr/share/cmake-3.18/Modules/CMakeDetermineSystem.cmake /usr/share/cmake-3.18/Modules/CMakeFindBinUtils.cmake /usr/share/cmake-3.18/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.18/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.18/Modules/CMakeLanguageInformation.cmake /usr/share/cmake-3.18/Modules/CMakeNinjaFindMake.cmake /usr/share/cmake-3.18/Modules/CMakeParseImplicitIncludeInfo.cmake /usr/share/cmake-3.18/Modules/CMakeParseImplicitLinkInfo.cmake /usr/share/cmake-3.18/Modules/CMakeSystem.cmake.in /usr/share/cmake-3.18/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.18/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.18/Modules/CMakeTestCCompiler.cmake /usr/share/cmake-3.18/Modules/CMakeTestCompilerCommon.cmake /usr/share/cmake-3.18/Modules/Compiler/ADSP-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/ARMCC-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/ARMClang-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/AppleClang-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/Borland-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/Bruce-C-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake-3.18/Modules/Compiler/Clang-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /usr/share/cmake-3.18/Modules/Compiler/Compaq-C-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/Cray-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/GHS-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/GNU-C-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/GNU-C.cmake /usr/share/cmake-3.18/Modules/Compiler/GNU-FindBinUtils.cmake /usr/share/cmake-3.18/Modules/Compiler/GNU.cmake /usr/share/cmake-3.18/Modules/Compiler/HP-C-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/IAR-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake /usr/share/cmake-3.18/Modules/Compiler/Intel-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/MSVC-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/PGI-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/PathScale-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/SCO-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/SDCC-C-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/SunPro-C-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/TI-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/Watcom-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/XL-C-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/XLClang-C-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/zOS-C-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Internal/CMakeCheckCompilerFlag.cmake /usr/share/cmake-3.18/Modules/Internal/FeatureTesting.cmake /usr/share/cmake-3.18/Modules/Platform/Linux-GNU-C.cmake /usr/share/cmake-3.18/Modules/Platform/Linux-GNU.cmake /usr/share/cmake-3.18/Modules/Platform/Linux.cmake /usr/share/cmake-3.18/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.18.4/CMakeCCompiler.cmake CMakeFiles/3.18.4/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build ../CMakeLists.txt /usr/share/cmake-3.18/Modules/CMakeCCompiler.cmake.in /usr/share/cmake-3.18/Modules/CMakeCCompilerABI.c /usr/share/cmake-3.18/Modules/CMakeCInformation.cmake /usr/share/cmake-3.18/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /usr/share/cmake-3.18/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake-3.18/Modules/CMakeCompilerIdDetection.cmake /usr/share/cmake-3.18/Modules/CMakeDetermineCCompiler.cmake /usr/share/cmake-3.18/Modules/CMakeDetermineCompileFeatures.cmake /usr/share/cmake-3.18/Modules/CMakeDetermineCompiler.cmake /usr/share/cmake-3.18/Modules/CMakeDetermineCompilerABI.cmake /usr/share/cmake-3.18/Modules/CMakeDetermineCompilerId.cmake /usr/share/cmake-3.18/Modules/CMakeDetermineSystem.cmake /usr/share/cmake-3.18/Modules/CMakeFindBinUtils.cmake /usr/share/cmake-3.18/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.18/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.18/Modules/CMakeLanguageInformation.cmake /usr/share/cmake-3.18/Modules/CMakeNinjaFindMake.cmake /usr/share/cmake-3.18/Modules/CMakeParseImplicitIncludeInfo.cmake /usr/share/cmake-3.18/Modules/CMakeParseImplicitLinkInfo.cmake /usr/share/cmake-3.18/Modules/CMakeSystem.cmake.in /usr/share/cmake-3.18/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.18/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.18/Modules/CMakeTestCCompiler.cmake /usr/share/cmake-3.18/Modules/CMakeTestCompilerCommon.cmake /usr/share/cmake-3.18/Modules/Compiler/ADSP-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/ARMCC-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/ARMClang-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/AppleClang-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/Borland-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/Bruce-C-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake-3.18/Modules/Compiler/Clang-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /usr/share/cmake-3.18/Modules/Compiler/Compaq-C-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/Cray-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/GHS-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/GNU-C-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/GNU-C.cmake /usr/share/cmake-3.18/Modules/Compiler/GNU-FindBinUtils.cmake /usr/share/cmake-3.18/Modules/Compiler/GNU.cmake /usr/share/cmake-3.18/Modules/Compiler/HP-C-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/IAR-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake /usr/share/cmake-3.18/Modules/Compiler/Intel-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/MSVC-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/PGI-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/PathScale-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/SCO-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/SDCC-C-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/SunPro-C-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/TI-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/Watcom-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/XL-C-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/XLClang-C-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Compiler/zOS-C-DetermineCompiler.cmake /usr/share/cmake-3.18/Modules/Internal/CMakeCheckCompilerFlag.cmake /usr/share/cmake-3.18/Modules/Internal/FeatureTesting.cmake /usr/share/cmake-3.18/Modules/Platform/Linux-GNU-C.cmake /usr/share/cmake-3.18/Modules/Platform/Linux-GNU.cmake /usr/share/cmake-3.18/Modules/Platform/Linux.cmake /usr/share/cmake-3.18/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.18.4/CMakeCCompiler.cmake CMakeFiles/3.18.4/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
