[{"directory": "/home/<USER>/led/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -g -o CMakeFiles/fan.dir/fan.c.o -c /home/<USER>/led/fan.c", "file": "/home/<USER>/led/fan.c"}, {"directory": "/home/<USER>/led/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -g -o CMakeFiles/demo.dir/demo.c.o -c /home/<USER>/led/demo.c", "file": "/home/<USER>/led/demo.c"}, {"directory": "/home/<USER>/led/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -g -o CMakeFiles/demo.dir/file.c.o -c /home/<USER>/led/file.c", "file": "/home/<USER>/led/file.c"}, {"directory": "/home/<USER>/led/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -g -o CMakeFiles/led1.dir/led.c.o -c /home/<USER>/led/led.c", "file": "/home/<USER>/led/led.c"}, {"directory": "/home/<USER>/led/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -g -o CMakeFiles/led1.dir/file.c.o -c /home/<USER>/led/file.c", "file": "/home/<USER>/led/file.c"}]