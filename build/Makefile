# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.18

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Disable VCS-based implicit rules.
% : %,v


# Disable VCS-based implicit rules.
% : RCS/%


# Disable VCS-based implicit rules.
% : RCS/%,v


# Disable VCS-based implicit rules.
% : SCCS/s.%


# Disable VCS-based implicit rules.
% : s.%


.SUFFIXES: .hpux_make_needs_suffix_list


# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/led

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/led/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/led/build/CMakeFiles /home/<USER>/led/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/led/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named fan

# Build rule for target.
fan: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 fan
.PHONY : fan

# fast build rule for target.
fan/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/fan.dir/build.make CMakeFiles/fan.dir/build
.PHONY : fan/fast

#=============================================================================
# Target rules for targets named demo

# Build rule for target.
demo: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 demo
.PHONY : demo

# fast build rule for target.
demo/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/demo.dir/build.make CMakeFiles/demo.dir/build
.PHONY : demo/fast

#=============================================================================
# Target rules for targets named led1

# Build rule for target.
led1: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 led1
.PHONY : led1

# fast build rule for target.
led1/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/led1.dir/build.make CMakeFiles/led1.dir/build
.PHONY : led1/fast

demo.o: demo.c.o

.PHONY : demo.o

# target to build an object file
demo.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/demo.dir/build.make CMakeFiles/demo.dir/demo.c.o
.PHONY : demo.c.o

demo.i: demo.c.i

.PHONY : demo.i

# target to preprocess a source file
demo.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/demo.dir/build.make CMakeFiles/demo.dir/demo.c.i
.PHONY : demo.c.i

demo.s: demo.c.s

.PHONY : demo.s

# target to generate assembly for a file
demo.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/demo.dir/build.make CMakeFiles/demo.dir/demo.c.s
.PHONY : demo.c.s

fan.o: fan.c.o

.PHONY : fan.o

# target to build an object file
fan.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/fan.dir/build.make CMakeFiles/fan.dir/fan.c.o
.PHONY : fan.c.o

fan.i: fan.c.i

.PHONY : fan.i

# target to preprocess a source file
fan.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/fan.dir/build.make CMakeFiles/fan.dir/fan.c.i
.PHONY : fan.c.i

fan.s: fan.c.s

.PHONY : fan.s

# target to generate assembly for a file
fan.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/fan.dir/build.make CMakeFiles/fan.dir/fan.c.s
.PHONY : fan.c.s

file.o: file.c.o

.PHONY : file.o

# target to build an object file
file.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/demo.dir/build.make CMakeFiles/demo.dir/file.c.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/led1.dir/build.make CMakeFiles/led1.dir/file.c.o
.PHONY : file.c.o

file.i: file.c.i

.PHONY : file.i

# target to preprocess a source file
file.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/demo.dir/build.make CMakeFiles/demo.dir/file.c.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/led1.dir/build.make CMakeFiles/led1.dir/file.c.i
.PHONY : file.c.i

file.s: file.c.s

.PHONY : file.s

# target to generate assembly for a file
file.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/demo.dir/build.make CMakeFiles/demo.dir/file.c.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/led1.dir/build.make CMakeFiles/led1.dir/file.c.s
.PHONY : file.c.s

led.o: led.c.o

.PHONY : led.o

# target to build an object file
led.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/led1.dir/build.make CMakeFiles/led1.dir/led.c.o
.PHONY : led.c.o

led.i: led.c.i

.PHONY : led.i

# target to preprocess a source file
led.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/led1.dir/build.make CMakeFiles/led1.dir/led.c.i
.PHONY : led.c.i

led.s: led.c.s

.PHONY : led.s

# target to generate assembly for a file
led.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/led1.dir/build.make CMakeFiles/led1.dir/led.c.s
.PHONY : led.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... demo"
	@echo "... fan"
	@echo "... led1"
	@echo "... demo.o"
	@echo "... demo.i"
	@echo "... demo.s"
	@echo "... fan.o"
	@echo "... fan.i"
	@echo "... fan.s"
	@echo "... file.o"
	@echo "... file.i"
	@echo "... file.s"
	@echo "... led.o"
	@echo "... led.i"
	@echo "... led.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

