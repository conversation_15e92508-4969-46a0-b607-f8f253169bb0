# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.18

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Disable VCS-based implicit rules.
% : %,v


# Disable VCS-based implicit rules.
% : RCS/%


# Disable VCS-based implicit rules.
% : RCS/%,v


# Disable VCS-based implicit rules.
% : SCCS/s.%


# Disable VCS-based implicit rules.
% : s.%


.SUFFIXES: .hpux_make_needs_suffix_list


# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/led

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/led/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/fan.dir/all
all: CMakeFiles/demo.dir/all
all: CMakeFiles/led1.dir/all

.PHONY : all

# The main recursive "preinstall" target.
preinstall:

.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/fan.dir/clean
clean: CMakeFiles/demo.dir/clean
clean: CMakeFiles/led1.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/fan.dir

# All Build rule for target.
CMakeFiles/fan.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/fan.dir/build.make CMakeFiles/fan.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/fan.dir/build.make CMakeFiles/fan.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/led/build/CMakeFiles --progress-num=4,5 "Built target fan"
.PHONY : CMakeFiles/fan.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/fan.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/led/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/fan.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/led/build/CMakeFiles 0
.PHONY : CMakeFiles/fan.dir/rule

# Convenience name for target.
fan: CMakeFiles/fan.dir/rule

.PHONY : fan

# clean rule for target.
CMakeFiles/fan.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/fan.dir/build.make CMakeFiles/fan.dir/clean
.PHONY : CMakeFiles/fan.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/demo.dir

# All Build rule for target.
CMakeFiles/demo.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/demo.dir/build.make CMakeFiles/demo.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/demo.dir/build.make CMakeFiles/demo.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/led/build/CMakeFiles --progress-num=1,2,3 "Built target demo"
.PHONY : CMakeFiles/demo.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/demo.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/led/build/CMakeFiles 3
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/demo.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/led/build/CMakeFiles 0
.PHONY : CMakeFiles/demo.dir/rule

# Convenience name for target.
demo: CMakeFiles/demo.dir/rule

.PHONY : demo

# clean rule for target.
CMakeFiles/demo.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/demo.dir/build.make CMakeFiles/demo.dir/clean
.PHONY : CMakeFiles/demo.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/led1.dir

# All Build rule for target.
CMakeFiles/led1.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/led1.dir/build.make CMakeFiles/led1.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/led1.dir/build.make CMakeFiles/led1.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/led/build/CMakeFiles --progress-num=6,7,8 "Built target led1"
.PHONY : CMakeFiles/led1.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/led1.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/led/build/CMakeFiles 3
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/led1.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/led/build/CMakeFiles 0
.PHONY : CMakeFiles/led1.dir/rule

# Convenience name for target.
led1: CMakeFiles/led1.dir/rule

.PHONY : led1

# clean rule for target.
CMakeFiles/led1.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/led1.dir/build.make CMakeFiles/led1.dir/clean
.PHONY : CMakeFiles/led1.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

