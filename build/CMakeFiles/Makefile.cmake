# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.18

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "../CMakeLists.txt"
  "CMakeFiles/3.18.4/CMakeCCompiler.cmake"
  "CMakeFiles/3.18.4/CMakeSystem.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeCCompiler.cmake.in"
  "/usr/share/cmake-3.18/Modules/CMakeCCompilerABI.c"
  "/usr/share/cmake-3.18/Modules/CMakeCInformation.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeCompilerIdDetection.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeDetermineCCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeDetermineCompileFeatures.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeDetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeDetermineCompilerABI.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeDetermineCompilerId.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeDetermineSystem.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeFindBinUtils.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeInitializeConfigs.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeParseImplicitIncludeInfo.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeParseImplicitLinkInfo.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeSystem.cmake.in"
  "/usr/share/cmake-3.18/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeTestCCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeTestCompilerCommon.cmake"
  "/usr/share/cmake-3.18/Modules/CMakeUnixFindMake.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/ADSP-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/ARMCC-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/ARMClang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/AppleClang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/Borland-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/Bruce-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/Clang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/Cray-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/GHS-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/GNU-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/GNU-C.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/GNU-FindBinUtils.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/HP-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/IAR-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/Intel-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/MSVC-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/PGI-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/PathScale-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/SCO-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/TI-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/Watcom-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/XL-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/XLClang-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Compiler/zOS-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.18/Modules/Internal/CMakeCheckCompilerFlag.cmake"
  "/usr/share/cmake-3.18/Modules/Internal/FeatureTesting.cmake"
  "/usr/share/cmake-3.18/Modules/Platform/Linux-GNU-C.cmake"
  "/usr/share/cmake-3.18/Modules/Platform/Linux-GNU.cmake"
  "/usr/share/cmake-3.18/Modules/Platform/Linux.cmake"
  "/usr/share/cmake-3.18/Modules/Platform/UnixPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/3.18.4/CMakeSystem.cmake"
  "CMakeFiles/3.18.4/CMakeCCompiler.cmake"
  "CMakeFiles/3.18.4/CMakeCCompiler.cmake"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/led_flow.dir/DependInfo.cmake"
  "CMakeFiles/fan.dir/DependInfo.cmake"
  "CMakeFiles/demo.dir/DependInfo.cmake"
  "CMakeFiles/led_simple.dir/DependInfo.cmake"
  "CMakeFiles/led1.dir/DependInfo.cmake"
  )
