#include <stdio.h>
#include <unistd.h>
#include <stdlib.h>

// 模拟LED闪烁
void led_blink_simulation(int times)
{
    for(int i = 0; i < times; i++)
    {
        printf("💡 LED亮 -> ");
        fflush(stdout);
        usleep(300000);
        printf("LED灭\n");
        usleep(300000);
    }
}

// 模拟风扇控制
void fan_control_simulation(int speed)
{
    if(speed > 0)
        printf("🌀 风扇启动 (速度: %d)\n", speed);
    else
        printf("🌀 风扇关闭\n");
}

int main()
{
    printf("=== 温湿度智能控制系统演示 ===\n");
    printf("温度阈值: 29.0°C\n");
    printf("湿度阈值: 70.0%%\n\n");
    
    // 模拟数据
    float temperatures[] = {25.5, 28.0, 30.5, 32.0, 29.5, 27.0};
    float humidities[] = {65.0, 68.0, 72.0, 75.0, 69.0, 63.0};
    
    int fan_running = 0;
    
    for(int cycle = 0; cycle < 6; cycle++)
    {
        float temperature = temperatures[cycle];
        float humidity = humidities[cycle];
        
        printf("=== 检测周期 %d ===\n", cycle + 1);
        printf("当前温度: %.1f°C\n", temperature);
        printf("当前湿度: %.1f%%\n", humidity);
        
        // 温度控制逻辑
        if(temperature > 29.0 && !fan_running)
        {
            printf("🌡️  温度过高！启动风扇...\n");
            fan_control_simulation(255);
            fan_running = 1;
        }
        else if(temperature <= 29.0 && fan_running)
        {
            printf("🌡️  温度正常，关闭风扇...\n");
            fan_control_simulation(0);
            fan_running = 0;
        }
        else if(fan_running)
        {
            printf("🌀 风扇运行中...\n");
        }
        else
        {
            printf("🌀 风扇关闭状态\n");
        }
        
        // 湿度控制逻辑
        if(humidity > 70.0)
        {
            printf("💧 湿度过高！LED警告闪烁...\n");
            led_blink_simulation(3);
        }
        else
        {
            printf("💡 LED正常状态\n");
        }
        
        printf("-------------------\n");
        sleep(2);
    }
    
    printf("\n✅ 演示完成！\n");
    printf("系统功能验证：\n");
    printf("✓ 温度监控和风扇自动控制\n");
    printf("✓ 湿度监控和LED警告\n");
    printf("✓ 实时数据处理和设备控制\n");
    
    return 0;
}
