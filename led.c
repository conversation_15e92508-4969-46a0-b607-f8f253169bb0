#include "file.h"
#include <unistd.h>
#include <stdio.h>

// 系统路径定义 - 根据实际硬件路径
const char *temp_path = "/sys/bus/iio/devices/iio:device0/in_temp_offset";
const char *humidity_path = "/sys/bus/iio/devices/iio:device0/in_humidityrelative_raw";

// 阈值设置
#define TEMP_THRESHOLD 29.0    // 温度阈值（摄氏度）
#define HUMIDITY_THRESHOLD 70.0 // 湿度阈值（百分比）
#define FAN_SPEED_HIGH 255     // 风扇高速
#define FAN_SPEED_OFF 0        // 风扇关闭



int main()
{
    float temperature = 0.0;
    float humidity = 0.0;
    int fan_running = 0;

    printf("温度湿度控制系统启动...\n");
    printf("温度阈值: %.1f°C\n", TEMP_THRESHOLD);
    printf("湿度阈值: %.1f%%\n", HUMIDITY_THRESHOLD);

    while(1)
    {
        // 模拟温度读取（实际部署时替换为真实传感器路径）
        static int cycle = 0;
        float simulated_temps[] = {25.5, 28.0, 30.5, 32.0, 29.5, 27.0};
        temperature = simulated_temps[cycle % 6];
        cycle++;

        printf("当前温度: %.2f°C (模拟数据)\n", temperature);

            // 温度控制逻辑
            if(temperature > TEMP_THRESHOLD && !fan_running)
            {
                printf("温度过高！启动风扇...\n");
                control_fan(FAN_SPEED_HIGH);
                fan_running = 1;
            }
            else if(temperature <= TEMP_THRESHOLD && fan_running)
            {
                printf("温度正常，关闭风扇...\n");
                control_fan(FAN_SPEED_OFF);
                fan_running = 0;
            }
        }

        // 模拟湿度读取（实际部署时替换为真实传感器路径）
        static int humidity_cycle = 0;
        float simulated_humidity[] = {65.0, 68.0, 72.0, 75.0, 69.0, 63.0};
        humidity = simulated_humidity[humidity_cycle % 6];
        humidity_cycle++;

        printf("当前湿度: %.2f%% (模拟数据)\n", humidity);

            // 湿度控制逻辑
            if(humidity > HUMIDITY_THRESHOLD)
            {
                printf("湿度过高！LED警告闪烁...\n");
                led_blink(3); // 闪烁3次
            }
        }

        printf("-------------------\n");
        sleep(5); // 每5秒检测一次
    }

    return 0;
}